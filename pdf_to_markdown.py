import pypdf
import sys
import os

def convert_pdf_to_markdown(pdf_path):
    """
    Converts a PDF file to a Markdown file by extracting its text.

    Args:
        pdf_path (str): The absolute path to the PDF file.
    """
    if not os.path.exists(pdf_path):
        print(f"Error: File not found at {pdf_path}")
        return

    try:
        # Open the PDF file
        with open(pdf_path, "rb") as pdf_file:
            reader = pypdf.PdfReader(pdf_file)
            text_content = []
            
            # Extract text from each page
            for i, page in enumerate(reader.pages):
                text_content.append(f"--- Page {i+1} ---\n")
                text_content.append(page.extract_text())
                text_content.append("\n\n")

        # Create the output file path
        base_name = os.path.basename(pdf_path)
        file_name_without_ext = os.path.splitext(base_name)[0]
        output_path = os.path.join(os.path.dirname(pdf_path), f"{file_name_without_ext}_script_generated.md")

        # Write the extracted text to the Markdown file
        with open(output_path, "w", encoding="utf-8") as md_file:
            md_file.write("".join(text_content))

        print(f"Successfully converted {pdf_path} to {output_path}")

    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python pdf_to_markdown.py <path_to_pdf_file>")
    else:
        pdf_path = sys.argv[1]
        convert_pdf_to_markdown(pdf_path)
