
# advanced_pdf_converter.py
#
# Description:
# A powerful PDF to Markdown converter that handles both text-based and image-based (scanned) PDFs.
# It uses layout analysis to identify and format headings, lists, and tables.
#
# Author: Gemini
# Date: 2025-08-04

import fitz  # PyMuPDF
import pytesseract
from pdf2image import convert_from_path
from PIL import Image
import os
import sys
import re
import statistics

# --- SETUP INSTRUCTIONS ---
#
# 1. INSTALL PYTHON LIBRARIES:
#    pip install PyMuPDF pdf2image pytesseract Pillow
#
# 2. INSTALL TESSERACT OCR ENGINE:
#    This script requires Google's Tesseract OCR engine to be installed on your system.
#    - Windows: Download and run the installer from https://github.com/UB-Mannheim/tesseract/wiki
#      (During installation, make sure to select the language packs you need, e.g., "Chinese - Simplified")
#    - macOS: brew install tesseract tesseract-lang
#    - Linux (Debian/Ubuntu): sudo apt-get install tesseract-ocr tesseract-ocr-chi-sim
#
#    **IMPORTANT**: After installing, you might need to add the Tesseract installation directory to the
#    system's PATH environment variable, or specify the path to the executable directly in this script below.
#
# 3. (WINDOWS-SPECIFIC) TESSERACT CMD PATH:
#    If the script can't find Tesseract, uncomment the line below and set the correct path to your tesseract.exe
#    pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
#
# 4. INSTALL POPPLER (for pdf2image):
#    `pdf2image` requires the Poppler utility to render PDF pages.
#    - Windows: Download the latest Poppler binary for Windows, extract it, and add the 'bin' folder to your PATH.
#      (e.g., from https://github.com/oschwartz10612/poppler-windows/releases/)
#    - macOS: brew install poppler
#    - Linux (Debian/Ubuntu): sudo apt-get install poppler-utils

def is_scanned_page(page, text_threshold=100):
    """
    Heuristic to check if a page is likely scanned.
    If a page has very little extractable text, it's probably an image.
    """
    return len(page.get_text().strip()) < text_threshold

def ocr_page(pdf_path, page_num):
    """
    Performs OCR on a specific page of a PDF.
    """
    print(f"  - Page {page_num + 1} has little text, performing OCR...")
    try:
        images = convert_from_path(pdf_path, first_page=page_num + 1, last_page=page_num + 1)
        if images:
            # Using both English and Simplified Chinese for OCR
            return pytesseract.image_to_string(images[0], lang='eng+chi_sim')
    except Exception as e:
        return f"\n[OCR Error on page {page_num + 1}: {e}]\n"
    return ""

def get_page_text_and_font_info(page):
    """
    Extracts detailed text blocks with font size and flags.
    """
    blocks = page.get_text("dict", flags=fitz.TEXTFLAGS_TEXT)["blocks"]
    # Calculate the most common font size to identify headings
    font_sizes = []
    if blocks:
        for b in blocks:
            for l in b.get("lines", []):
                for s in l.get("spans", []):
                    font_sizes.append(round(s["size"]))
    
    base_font_size = statistics.mode(font_sizes) if font_sizes else 12.0
    return blocks, base_font_size

def format_tables(page):
    """
    Detects and formats tables into Markdown.
    """
    tables = page.find_tables()
    md_tables = []
    if tables:
        print(f"  - Found {len(tables.tables)} table(s) on page.")
        for table in tables:
            df = table.to_pandas()
            # Replace None with empty string
            df = df.fillna('')
            # Convert to markdown
            md_table = "\n" + df.to_markdown(index=False) + "\n"
            md_tables.append(md_table)
    return md_tables

def convert_pdf_to_markdown(pdf_path):
    """
    Converts a PDF file to a Markdown file with advanced layout detection.
    """
    if not os.path.exists(pdf_path):
        print(f"Error: File not found at {pdf_path}")
        return

    print(f"Processing {os.path.basename(pdf_path)}...")
    doc = fitz.open(pdf_path)
    markdown_content = []

    for i, page in enumerate(doc):
        print(f"- Processing page {i + 1}/{len(doc)}")
        
        # 1. Handle Tables First
        md_tables = format_tables(page)
        # We can add a placeholder and replace it later, or just append.
        # For simplicity, we'll append them after processing other text.

        # 2. Decide whether to use OCR
        if is_scanned_page(page):
            page_text = ocr_page(pdf_path, i)
            # Since we don't have font info from OCR, we just append the raw text
            markdown_content.append(f"\n--- Page {i+1} (OCR) ---\n\n" + page_text)
            markdown_content.extend(md_tables) # Add any tables found
            continue

        # 3. Process text-based pages with layout analysis
        blocks, base_font_size = get_page_text_and_font_info(page)
        
        page_md = [f"\n--- Page {i+1} ---\n"]
        
        for b in blocks:
            block_text_lines = []
            for l in b.get("lines", []):
                line_spans = []
                for s in l.get("spans", []):
                    # Heuristic for headings: font size is larger than the page average
                    if s["size"] > base_font_size * 1.2:
                        # Use ## for headings
                        line_spans.append(f"## {s['text']}")
                    # Heuristic for bold text (might not be a heading)
                    elif s["flags"] & 2**4: # 2**4 is the bold flag
                        line_spans.append(f"**{s['text']}**")
                    else:
                        line_spans.append(s['text'])
                
                block_text_lines.append("".join(line_spans))
            
            block_text = "\n".join(block_text_lines)
            
            # Heuristic for lists
            # This regex looks for lines starting with markers like 1. a. - *
            if re.match(r'^\s*([\*\-]|
\d+\.|[a-zA-Z]\.)\s+', block_text):
                page_md.append("- " + block_text.strip())
            else:
                page_md.append(block_text)
        
        markdown_content.append("\n".join(page_md))
        markdown_content.extend(md_tables)

    # 4. Finalize and save
    output_path = os.path.splitext(pdf_path)[0] + "_advanced.md"
    try:
        with open(output_path, "w", encoding="utf-8") as md_file:
            md_file.write("\n".join(markdown_content))
        print(f"\nSuccessfully converted to {output_path}")
    except Exception as e:
        print(f"\nError writing to file: {e}")

    doc.close()


if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python advanced_pdf_converter.py <absolute_path_to_pdf_file>")
    else:
        pdf_path = sys.argv[1]
        convert_pdf_to_markdown(pdf_path)
